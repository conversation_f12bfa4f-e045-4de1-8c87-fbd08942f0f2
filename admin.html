<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐺 The Wolf Challenge - Admin Panel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" type="image/x-icon" href="favicon.ico">
</head>
<body class="bg-gray-100 font-mono">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-black text-white flex items-center justify-center z-50">
        <div class="text-center">
            <div class="text-6xl mb-4">🐺</div>
            <div class="text-2xl font-bold mb-4">THE WOLF CHALLENGE</div>
            <div class="text-lg mb-4">ADMIN PANEL</div>
            <div class="loading-dots">
                <span>.</span><span>.</span><span>.</span>
            </div>
        </div>
    </div>

    <!-- Admin Login Screen -->
    <div id="admin-login-screen" class="hidden fixed inset-0 bg-gradient-to-br from-red-900 to-black text-white flex items-center justify-center z-40">
        <div class="neo-brutalist bg-white text-black p-8 max-w-md w-full mx-4">
            <div class="text-center mb-6">
                <div class="text-4xl mb-2">🐺</div>
                <h1 class="text-2xl font-bold">ADMIN ACCESS</h1>
                <p class="text-sm text-gray-600">Authorized Personnel Only</p>
            </div>

            <form id="admin-login-form" class="space-y-4">
                <div>
                    <label class="block text-sm font-bold mb-2">Admin Email</label>
                    <input type="email" id="admin-email" required
                           class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                           placeholder="<EMAIL>">
                </div>
                
                <div>
                    <label class="block text-sm font-bold mb-2">Admin Password</label>
                    <input type="password" id="admin-password" required
                           class="neo-brutalist w-full p-3 bg-white border-2 border-black"
                           placeholder="Enter admin password">
                </div>

                <button type="submit" class="neo-brutalist w-full bg-red-500 text-white p-3 font-bold hover:bg-red-600">
                    <i class="fas fa-sign-in-alt mr-2"></i>ACCESS ADMIN PANEL
                </button>
            </form>

            <div class="mt-4 text-center">
                <button onclick="showQuickLogin()" class="text-sm text-blue-600 hover:underline">
                    Quick Login (Development)
                </button>
            </div>
        </div>
    </div>

    <!-- Main Admin Panel -->
    <div id="admin-panel" class="hidden min-h-screen">
        <!-- Header -->
        <header class="neo-brutalist bg-red-500 text-white p-4">
            <div class="container mx-auto flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <div class="text-2xl">🐺</div>
                    <div>
                        <h1 class="text-xl font-bold">THE WOLF CHALLENGE</h1>
                        <p class="text-sm">Admin Control Panel</p>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <div class="text-right">
                        <div id="admin-email-display" class="text-sm">[HIDDEN FOR SECURITY]</div>
                        <div class="text-xs bg-yellow-400 text-black px-2 py-1 rounded font-bold">ADMIN</div>
                    </div>
                    <button onclick="adminLogout()" class="neo-brutalist bg-black text-white px-4 py-2 text-sm">
                        <i class="fas fa-sign-out-alt mr-1"></i>LOGOUT
                    </button>
                </div>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="neo-brutalist bg-white border-b-4 border-black">
            <div class="container mx-auto">
                <div class="flex space-x-1">
                    <button id="overview-tab" class="nav-tab active neo-brutalist bg-blue-500 text-white px-6 py-3 font-bold">
                        <i class="fas fa-tachometer-alt mr-2"></i>OVERVIEW
                    </button>
                    <button id="users-tab" class="nav-tab neo-brutalist bg-gray-300 text-black px-6 py-3 font-bold">
                        <i class="fas fa-users mr-2"></i>USERS
                    </button>
                    <button id="challenges-tab" class="nav-tab neo-brutalist bg-gray-300 text-black px-6 py-3 font-bold">
                        <i class="fas fa-flag mr-2"></i>CHALLENGES
                    </button>
                    <button id="submissions-tab" class="nav-tab neo-brutalist bg-gray-300 text-black px-6 py-3 font-bold">
                        <i class="fas fa-paper-plane mr-2"></i>SUBMISSIONS
                    </button>
                    <button id="notifications-tab" class="nav-tab neo-brutalist bg-gray-300 text-black px-6 py-3 font-bold">
                        <i class="fas fa-bell mr-2"></i>NOTIFICATIONS
                    </button>
                    <button id="system-tab" class="nav-tab neo-brutalist bg-gray-300 text-black px-6 py-3 font-bold">
                        <i class="fas fa-cogs mr-2"></i>SYSTEM
                    </button>
                </div>
            </div>
        </nav>

        <!-- Content Area -->
        <main class="container mx-auto p-6">
            <div id="admin-content">
                <!-- Content will be loaded here by admin.js -->
                <div class="text-center py-12">
                    <div class="text-4xl mb-4">🐺</div>
                    <div class="text-xl font-bold">Loading Admin Panel...</div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script type="module" src="firebase-config.js"></script>
    <script type="module" src="admin-auth.js"></script>
    <script type="module" src="admin-panel.js"></script>

    <script>
        // Quick login for development
        function showQuickLogin() {
            document.getElementById('admin-email').value = '<EMAIL>';
            document.getElementById('admin-password').value = 'admintamilselvan';
        }

        // Admin logout
        function adminLogout() {
            if (confirm('Are you sure you want to logout?')) {
                // Clear session
                localStorage.removeItem('adminSession');
                sessionStorage.clear();
                
                // Redirect to login
                document.getElementById('admin-panel').classList.add('hidden');
                document.getElementById('admin-login-screen').classList.remove('hidden');
                
                // Sign out from Firebase
                if (window.auth && window.auth.currentUser) {
                    window.auth.signOut();
                }
            }
        }

        // Navigation tab switching
        document.addEventListener('DOMContentLoaded', function() {
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    tabs.forEach(t => {
                        t.classList.remove('active', 'bg-blue-500', 'text-white');
                        t.classList.add('bg-gray-300', 'text-black');
                    });
                    
                    // Add active class to clicked tab
                    this.classList.add('active', 'bg-blue-500', 'text-white');
                    this.classList.remove('bg-gray-300', 'text-black');
                    
                    // Load corresponding content
                    const tabId = this.id.replace('-tab', '');
                    if (window.adminPanel) {
                        window.adminPanel.loadSection(tabId);
                    }
                });
            });
        });

        // Hide loading screen after page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('loading-screen').classList.add('hidden');
                document.getElementById('admin-login-screen').classList.remove('hidden');
            }, 1000);
        });
    </script>
</body>
</html>
