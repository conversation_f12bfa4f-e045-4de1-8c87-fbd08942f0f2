// Create New Admin Account <PERSON><PERSON>t
// Run this in browser console to create the new admin account

console.log('🐺 === NEW ADMIN ACCOUNT CREATION ===');
console.log('🔧 Creating admin account for: <EMAIL>');
console.log('🔑 Password: admintamilselvan');
console.log('');

// New admin creation function
window.createNewAdmin = async function() {
  try {
    console.log('🚀 Starting new admin account creation...');
    
    // Import Firebase modules
    const { signInWithEmailAndPassword, createUserWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js');
    const { doc, setDoc, serverTimestamp, deleteDoc, getDocs, collection, query, where } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js');
    
    // New admin credentials
    const newAdminEmail = '<EMAIL>';
    const newAdminPassword = 'admintamilselvan';
    
    // Get Firebase instances
    let auth = window.auth;
    let db = window.db;
    
    if (!auth || !db) {
      console.log('🔄 Initializing Firebase...');
      
      const { initializeApp } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-app.js');
      const { getAuth } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js');
      const { getFirestore } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js');
      
      const firebaseConfig = {
        apiKey: "AIzaSyBWZWojxhjI3IWNo1km_XVcTXX5vYUV6Cc",
        authDomain: "wolf-ctf.firebaseapp.com",
        projectId: "wolf-ctf",
        storageBucket: "wolf-ctf.firebasestorage.app",
        messagingSenderId: "************",
        appId: "1:************:web:acb648a6bc2697ea1c9193"
      };
      
      const app = initializeApp(firebaseConfig);
      auth = getAuth(app);
      db = getFirestore(app);
      window.auth = auth;
      window.db = db;
      
      console.log('✅ Firebase initialized');
    }
    
    // Step 1: Remove old admin accounts
    console.log('🗑️ Removing old admin accounts...');
    try {
      const oldAdminEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];
      
      // Query for old admin users
      const usersRef = collection(db, 'users');
      const adminQuery = query(usersRef, where('role', '==', 'admin'));
      const adminSnapshot = await getDocs(adminQuery);
      
      for (const adminDoc of adminSnapshot.docs) {
        const adminData = adminDoc.data();
        if (oldAdminEmails.includes(adminData.email)) {
          await deleteDoc(doc(db, 'users', adminDoc.id));
          console.log('🗑️ Removed old admin:', adminData.email);
        }
      }
      
      console.log('✅ Old admin accounts removed');
    } catch (error) {
      console.log('⚠️ Could not remove old admins (they may not exist):', error.message);
    }
    
    // Step 2: Create new admin account
    let userCredential;
    let isNewUser = false;
    
    try {
      console.log('🔐 Creating new admin account...');
      userCredential = await createUserWithEmailAndPassword(auth, newAdminEmail, newAdminPassword);
      isNewUser = true;
      console.log('✅ New admin account created');
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        console.log('👤 Admin account already exists, signing in...');
        try {
          userCredential = await signInWithEmailAndPassword(auth, newAdminEmail, newAdminPassword);
          console.log('✅ Signed in with existing admin account');
        } catch (signInError) {
          if (signInError.code === 'auth/wrong-password') {
            console.log('🔄 Password mismatch detected');
            throw new Error('Admin account exists but password is incorrect. Please use Firebase Console to reset password.');
          } else {
            throw signInError;
          }
        }
      } else {
        throw error;
      }
    }
    
    const user = userCredential.user;
    console.log('👤 New Admin UID:', user.uid);
    console.log('📧 New Admin Email: <EMAIL>');
    
    // Step 3: Create admin document in Firestore
    console.log('📝 Creating admin user document in Firestore...');
    
    const userRef = doc(db, 'users', user.uid);
    const adminData = {
      email: newAdminEmail,
      role: 'admin',
      displayName: 'Tamil Selvan (Admin)',
      score: 0,
      challengesSolved: 0,
      solvedChallenges: [],
      progress: {
        beginner: { solved: 0, total: 20 },
        intermediate: { solved: 0, total: 25 },
        advanced: { solved: 0, total: 25 }
      },
      createdAt: serverTimestamp(),
      lastActivity: serverTimestamp(),
      rank: null,
      adminLevel: 'super',
      permissions: ['all'],
      isActive: true
    };
    
    await setDoc(userRef, adminData, { merge: true });
    console.log('✅ Admin user document created in Firestore');
    
    // Step 4: Update local auth manager if available
    if (window.authManager) {
      console.log('🔄 Updating local auth manager...');
      window.authManager.currentUser = user;
      window.authManager.userRole = 'admin';
      window.authManager.updateUserInterface();
      console.log('✅ Auth manager updated');
    }
    
    // Step 5: Show admin interface
    console.log('🎛️ Activating admin interface...');
    
    // Show admin tab if on main page
    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
      adminTab.classList.remove('hidden');
      adminTab.click();
      console.log('✅ Admin tab activated');
    }
    
    // Show admin section
    const adminSection = document.getElementById('admin-section');
    if (adminSection) {
      adminSection.classList.remove('hidden');
    }
    
    // Hide login screen, show main app
    const loginScreen = document.getElementById('login-screen');
    const mainApp = document.getElementById('main-app');
    
    if (loginScreen) loginScreen.classList.add('hidden');
    if (mainApp) mainApp.classList.remove('hidden');
    
    // Hide other sections
    ['challenges-section', 'leaderboard-section', 'profile-section'].forEach(id => {
      const section = document.getElementById(id);
      if (section) section.classList.add('hidden');
    });
    
    // Load admin manager if available
    if (window.adminManager) {
      window.adminManager.loadOverview();
      console.log('✅ Admin manager loaded');
    }
    
    console.log('');
    console.log('🎉 === NEW ADMIN ACCOUNT CREATED SUCCESSFULLY ===');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admintamilselvan');
    console.log('👑 Role: admin');
    console.log('🆔 UID:', user.uid);
    console.log('✅ Admin access activated');
    console.log('🗑️ Old admin accounts removed');
    console.log('');
    
    // Show success alert
    alert('🎉 New Admin Account Created Successfully!\n\n📧 Email: <EMAIL>\n🔑 Password: admintamilselvan\n👑 Role: admin\n\n✅ Admin panel is now active!\n🗑️ Old admin accounts have been removed');
    
    return {
      success: true,
      user: user,
      isNewUser: isNewUser,
      email: newAdminEmail,
      role: 'admin'
    };
    
  } catch (error) {
    console.error('❌ Error creating new admin account:', error);
    alert('❌ Error creating new admin account: ' + error.message);
    
    return {
      success: false,
      error: error.message
    };
  }
};

// Quick access to admin.html
window.openAdminPanel = function() {
  console.log('🔗 Opening dedicated admin panel...');
  window.open('./admin.html', '_blank');
};

// Auto-run the new admin creation
console.log('🚀 Auto-running new admin account creation...');
createNewAdmin().then(result => {
  if (result.success) {
    console.log('🎉 New admin account creation completed successfully!');
    console.log('');
    console.log('🔗 You can also access the dedicated admin panel at:');
    console.log('   👉 admin.html');
    console.log('');
    console.log('💡 To open the dedicated admin panel, run:');
    console.log('   👉 openAdminPanel()');
  } else {
    console.log('❌ New admin account creation failed:', result.error);
  }
});

// Make functions globally available
window.newAdmin = createNewAdmin;
window.adminPanel = openAdminPanel;
