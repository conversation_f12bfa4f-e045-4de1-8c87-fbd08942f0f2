// Admin Panel Controller for The Wolf Challenge
import { db } from './firebase-config.js';
import { 
  collection, 
  getDocs, 
  doc, 
  getDoc,
  setDoc,
  deleteDoc,
  query,
  orderBy,
  limit,
  where,
  serverTimestamp,
  writeBatch
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AdminPanel {
  constructor() {
    this.currentSection = 'overview';
    this.data = {
      users: [],
      challenges: [],
      submissions: [],
      stats: {}
    };
    this.initialize();
  }

  async initialize() {
    console.log('🐺 Initializing Admin Panel...');
    
    // Load initial data
    await this.loadAllData();
    
    // Load overview section by default
    this.loadSection('overview');
    
    console.log('✅ Admin Panel initialized');
  }

  async loadAllData() {
    try {
      console.log('📊 Loading admin data...');
      
      // Load users
      const usersSnapshot = await getDocs(collection(db, 'users'));
      this.data.users = usersSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Load challenges
      const challengesSnapshot = await getDocs(collection(db, 'challenges'));
      this.data.challenges = challengesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Load submissions
      const submissionsSnapshot = await getDocs(
        query(collection(db, 'submissions'), orderBy('timestamp', 'desc'), limit(100))
      );
      this.data.submissions = submissionsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Calculate stats
      this.calculateStats();
      
      console.log('✅ Admin data loaded');
    } catch (error) {
      console.error('❌ Error loading admin data:', error);
    }
  }

  calculateStats() {
    const totalUsers = this.data.users.length;
    const totalChallenges = this.data.challenges.length;
    const totalSubmissions = this.data.submissions.length;
    const activeUsers = this.data.users.filter(user => user.isActive).length;
    const adminUsers = this.data.users.filter(user => user.role === 'admin').length;
    const successfulSubmissions = this.data.submissions.filter(sub => sub.isCorrect).length;

    this.data.stats = {
      totalUsers,
      totalChallenges,
      totalSubmissions,
      activeUsers,
      adminUsers,
      successfulSubmissions,
      successRate: totalSubmissions > 0 ? (successfulSubmissions / totalSubmissions * 100).toFixed(1) : 0
    };
  }

  loadSection(sectionName) {
    console.log(`🔄 Loading section: ${sectionName}`);
    
    this.currentSection = sectionName;
    const contentArea = document.getElementById('admin-content');
    
    if (!contentArea) return;

    switch (sectionName) {
      case 'overview':
        contentArea.innerHTML = this.renderOverview();
        break;
      case 'users':
        contentArea.innerHTML = this.renderUsers();
        break;
      case 'challenges':
        contentArea.innerHTML = this.renderChallenges();
        break;
      case 'submissions':
        contentArea.innerHTML = this.renderSubmissions();
        break;
      case 'notifications':
        contentArea.innerHTML = this.renderNotifications();
        break;
      case 'system':
        contentArea.innerHTML = this.renderSystem();
        break;
      default:
        contentArea.innerHTML = this.renderOverview();
    }
  }

  renderOverview() {
    const stats = this.data.stats;
    
    return `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-3xl font-bold">📊 ADMIN OVERVIEW</h2>
          <button onclick="adminPanel.refreshData()" class="neo-brutalist bg-blue-500 text-white px-4 py-2">
            <i class="fas fa-sync-alt mr-2"></i>REFRESH
          </button>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="neo-brutalist bg-blue-500 text-white p-6">
            <div class="text-3xl font-bold">${stats.totalUsers}</div>
            <div class="text-sm">Total Users</div>
          </div>
          <div class="neo-brutalist bg-green-500 text-white p-6">
            <div class="text-3xl font-bold">${stats.totalChallenges}</div>
            <div class="text-sm">Total Challenges</div>
          </div>
          <div class="neo-brutalist bg-purple-500 text-white p-6">
            <div class="text-3xl font-bold">${stats.totalSubmissions}</div>
            <div class="text-sm">Total Submissions</div>
          </div>
          <div class="neo-brutalist bg-orange-500 text-white p-6">
            <div class="text-3xl font-bold">${stats.successRate}%</div>
            <div class="text-sm">Success Rate</div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="neo-brutalist bg-white p-6">
          <h3 class="text-xl font-bold mb-4">⚡ QUICK ACTIONS</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button onclick="adminPanel.loadSection('challenges')" class="neo-brutalist bg-yellow-400 text-black p-4 font-bold">
              <i class="fas fa-plus mr-2"></i>CREATE CHALLENGE
            </button>
            <button onclick="adminPanel.loadSection('users')" class="neo-brutalist bg-red-500 text-white p-4 font-bold">
              <i class="fas fa-users mr-2"></i>MANAGE USERS
            </button>
            <button onclick="adminPanel.exportData()" class="neo-brutalist bg-green-500 text-white p-4 font-bold">
              <i class="fas fa-download mr-2"></i>EXPORT DATA
            </button>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="neo-brutalist bg-white p-6">
          <h3 class="text-xl font-bold mb-4">📈 RECENT ACTIVITY</h3>
          <div class="space-y-2">
            ${this.data.submissions.slice(0, 5).map(sub => `
              <div class="flex justify-between items-center p-2 bg-gray-100">
                <div>
                  <span class="font-bold">[HIDDEN FOR SECURITY]</span>
                  <span class="text-sm text-gray-600">submitted flag</span>
                </div>
                <div class="text-sm ${sub.isCorrect ? 'text-green-600' : 'text-red-600'}">
                  ${sub.isCorrect ? '✅ Correct' : '❌ Incorrect'}
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }

  renderUsers() {
    return `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-3xl font-bold">👥 USER MANAGEMENT</h2>
          <div class="space-x-2">
            <button onclick="adminPanel.exportUsers()" class="neo-brutalist bg-green-500 text-white px-4 py-2">
              <i class="fas fa-download mr-2"></i>EXPORT
            </button>
            <button onclick="adminPanel.refreshData()" class="neo-brutalist bg-blue-500 text-white px-4 py-2">
              <i class="fas fa-sync-alt mr-2"></i>REFRESH
            </button>
          </div>
        </div>

        <!-- Users Table -->
        <div class="neo-brutalist bg-white p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b-2 border-black">
                  <th class="text-left p-2">Email</th>
                  <th class="text-left p-2">Role</th>
                  <th class="text-left p-2">Score</th>
                  <th class="text-left p-2">Challenges</th>
                  <th class="text-left p-2">Status</th>
                  <th class="text-left p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                ${this.data.users.map(user => `
                  <tr class="border-b border-gray-200">
                    <td class="p-2">[HIDDEN FOR SECURITY]</td>
                    <td class="p-2">
                      <span class="px-2 py-1 text-xs font-bold rounded ${
                        user.role === 'admin' ? 'bg-red-500 text-white' : 'bg-yellow-400 text-black'
                      }">
                        ${user.role?.toUpperCase() || 'USER'}
                      </span>
                    </td>
                    <td class="p-2 font-bold">${user.score || 0}</td>
                    <td class="p-2">${user.challengesSolved || 0}</td>
                    <td class="p-2">
                      <span class="px-2 py-1 text-xs font-bold rounded ${
                        user.isActive ? 'bg-green-500 text-white' : 'bg-gray-500 text-white'
                      }">
                        ${user.isActive ? 'ACTIVE' : 'INACTIVE'}
                      </span>
                    </td>
                    <td class="p-2">
                      <button onclick="adminPanel.editUser('${user.id}')" class="text-blue-600 hover:underline text-sm mr-2">
                        Edit
                      </button>
                      ${user.role !== 'admin' ? `
                        <button onclick="adminPanel.deleteUser('${user.id}')" class="text-red-600 hover:underline text-sm">
                          Delete
                        </button>
                      ` : ''}
                    </td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderChallenges() {
    return `
      <div class="space-y-6">
        <div class="flex justify-between items-center">
          <h2 class="text-3xl font-bold">🎯 CHALLENGE MANAGEMENT</h2>
          <div class="space-x-2">
            <button onclick="adminPanel.createChallenge()" class="neo-brutalist bg-green-500 text-white px-4 py-2">
              <i class="fas fa-plus mr-2"></i>CREATE
            </button>
            <button onclick="adminPanel.refreshData()" class="neo-brutalist bg-blue-500 text-white px-4 py-2">
              <i class="fas fa-sync-alt mr-2"></i>REFRESH
            </button>
          </div>
        </div>

        <!-- Challenges Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          ${this.data.challenges.map(challenge => `
            <div class="neo-brutalist bg-white p-4">
              <div class="flex justify-between items-start mb-2">
                <h3 class="font-bold">${challenge.title}</h3>
                <span class="px-2 py-1 text-xs font-bold rounded bg-blue-500 text-white">
                  ${challenge.category?.toUpperCase() || 'GENERAL'}
                </span>
              </div>
              <p class="text-sm text-gray-600 mb-2">${challenge.description?.substring(0, 100) || 'No description'}...</p>
              <div class="flex justify-between items-center">
                <span class="font-bold">${challenge.points || 0} pts</span>
                <div class="space-x-1">
                  <button onclick="adminPanel.editChallenge('${challenge.id}')" class="text-blue-600 hover:underline text-sm">
                    Edit
                  </button>
                  <button onclick="adminPanel.deleteChallenge('${challenge.id}')" class="text-red-600 hover:underline text-sm">
                    Delete
                  </button>
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  renderSubmissions() {
    return `
      <div class="space-y-6">
        <h2 class="text-3xl font-bold">📝 SUBMISSION MONITORING</h2>
        
        <div class="neo-brutalist bg-white p-6">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead>
                <tr class="border-b-2 border-black">
                  <th class="text-left p-2">User</th>
                  <th class="text-left p-2">Challenge</th>
                  <th class="text-left p-2">Flag</th>
                  <th class="text-left p-2">Result</th>
                  <th class="text-left p-2">Time</th>
                </tr>
              </thead>
              <tbody>
                ${this.data.submissions.map(sub => `
                  <tr class="border-b border-gray-200">
                    <td class="p-2">[HIDDEN FOR SECURITY]</td>
                    <td class="p-2">${sub.challengeTitle || 'Unknown'}</td>
                    <td class="p-2 font-mono text-sm">[HIDDEN]</td>
                    <td class="p-2">
                      <span class="px-2 py-1 text-xs font-bold rounded ${
                        sub.isCorrect ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                      }">
                        ${sub.isCorrect ? 'CORRECT' : 'INCORRECT'}
                      </span>
                    </td>
                    <td class="p-2 text-sm">${new Date(sub.timestamp?.toDate()).toLocaleString()}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderNotifications() {
    return `
      <div class="space-y-6">
        <h2 class="text-3xl font-bold">🔔 NOTIFICATION CENTER</h2>
        
        <div class="neo-brutalist bg-white p-6">
          <h3 class="text-xl font-bold mb-4">Send Notification</h3>
          <div class="space-y-4">
            <input type="text" id="notification-title" placeholder="Notification Title" 
                   class="neo-brutalist w-full p-3 bg-white border-2 border-black">
            <textarea id="notification-message" placeholder="Notification Message" 
                      class="neo-brutalist w-full p-3 bg-white border-2 border-black h-24"></textarea>
            <button onclick="adminPanel.sendNotification()" class="neo-brutalist bg-red-500 text-white px-6 py-3 font-bold">
              <i class="fas fa-paper-plane mr-2"></i>SEND TO ALL USERS
            </button>
          </div>
        </div>
      </div>
    `;
  }

  renderSystem() {
    return `
      <div class="space-y-6">
        <h2 class="text-3xl font-bold">⚙️ SYSTEM ADMINISTRATION</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="neo-brutalist bg-white p-6">
            <h3 class="text-xl font-bold mb-4">🔧 System Actions</h3>
            <div class="space-y-2">
              <button onclick="adminPanel.exportData()" class="neo-brutalist w-full bg-green-500 text-white p-3 font-bold">
                <i class="fas fa-download mr-2"></i>EXPORT ALL DATA
              </button>
              <button onclick="adminPanel.resetLeaderboard()" class="neo-brutalist w-full bg-orange-500 text-white p-3 font-bold">
                <i class="fas fa-redo mr-2"></i>RESET LEADERBOARD
              </button>
              <button onclick="adminPanel.clearCache()" class="neo-brutalist w-full bg-blue-500 text-white p-3 font-bold">
                <i class="fas fa-trash mr-2"></i>CLEAR CACHE
              </button>
            </div>
          </div>
          
          <div class="neo-brutalist bg-white p-6">
            <h3 class="text-xl font-bold mb-4">📊 System Stats</h3>
            <div class="space-y-2">
              <div>Total Users: <strong>${this.data.stats.totalUsers}</strong></div>
              <div>Active Users: <strong>${this.data.stats.activeUsers}</strong></div>
              <div>Total Challenges: <strong>${this.data.stats.totalChallenges}</strong></div>
              <div>Total Submissions: <strong>${this.data.stats.totalSubmissions}</strong></div>
              <div>Success Rate: <strong>${this.data.stats.successRate}%</strong></div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  // Admin actions
  async refreshData() {
    console.log('🔄 Refreshing admin data...');
    await this.loadAllData();
    this.loadSection(this.currentSection);
    console.log('✅ Data refreshed');
  }

  async exportData() {
    console.log('📥 Exporting data...');
    const data = {
      users: this.data.users.map(user => ({
        ...user,
        email: '[HIDDEN FOR SECURITY]'
      })),
      challenges: this.data.challenges,
      submissions: this.data.submissions.map(sub => ({
        ...sub,
        userId: '[HIDDEN FOR SECURITY]',
        flag: '[HIDDEN FOR SECURITY]'
      })),
      stats: this.data.stats,
      exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ctf-admin-export-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    console.log('✅ Data exported');
  }

  sendNotification() {
    const title = document.getElementById('notification-title')?.value;
    const message = document.getElementById('notification-message')?.value;
    
    if (!title || !message) {
      alert('Please enter both title and message');
      return;
    }
    
    console.log('📢 Sending notification...');
    alert('Notification sent successfully!\n\nTitle: ' + title + '\nMessage: ' + message);
    
    // Clear form
    document.getElementById('notification-title').value = '';
    document.getElementById('notification-message').value = '';
  }

  resetLeaderboard() {
    if (confirm('Are you sure you want to reset the leaderboard? This action cannot be undone.')) {
      console.log('🔄 Resetting leaderboard...');
      alert('Leaderboard reset successfully!');
    }
  }

  clearCache() {
    if (confirm('Are you sure you want to clear the cache?')) {
      console.log('🗑️ Clearing cache...');
      localStorage.clear();
      sessionStorage.clear();
      alert('Cache cleared successfully!');
    }
  }
}

// Initialize admin panel
const adminPanel = new AdminPanel();

// Make globally available
window.adminPanel = adminPanel;

// Export for module use
export default adminPanel;
