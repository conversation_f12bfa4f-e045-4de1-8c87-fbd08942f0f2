// Create Specific Admin <NAME_EMAIL>
// Run this script in the browser console to create the admin account

console.log('🐺 === ADMIN ACCOUNT CREATION SCRIPT ===');
console.log('🐺 Creating admin account for: <EMAIL>');
console.log('');

// Admin creation function
window.createSpecificAdmin = async function() {
  try {
    console.log('🚀 Starting admin account creation...');
    
    // Import Firebase modules
    const { signInWithEmailAndPassword, createUserWithEmailAndPassword, updatePassword } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js');
    const { doc, setDoc, serverTimestamp, getDoc } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js');
    
    // Admin credentials
    const adminEmail = '<EMAIL>';
    const adminPassword = 'tamilselvanadmin63';
    
    console.log('📧 Email:', adminEmail);
    console.log('🔑 Password:', adminPassword);
    console.log('');
    
    // Get Firebase instances
    let auth = window.auth;
    let db = window.db;
    
    if (!auth || !db) {
      console.log('🔄 Initializing Firebase...');
      
      // Initialize Firebase if not available
      const { initializeApp } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-app.js');
      const { getAuth } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js');
      const { getFirestore } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js');
      
      const firebaseConfig = {
        apiKey: "AIzaSyBWZWojxhjI3IWNo1km_XVcTXX5vYUV6Cc",
        authDomain: "wolf-ctf.firebaseapp.com",
        databaseURL: "https://wolf-ctf-default-rtdb.asia-southeast1.firebasedatabase.app",
        projectId: "wolf-ctf",
        storageBucket: "wolf-ctf.firebasestorage.app",
        messagingSenderId: "************",
        appId: "1:************:web:acb648a6bc2697ea1c9193",
        measurementId: "G-32N69ELWZN"
      };
      
      const app = initializeApp(firebaseConfig);
      auth = getAuth(app);
      db = getFirestore(app);
      
      window.auth = auth;
      window.db = db;
      
      console.log('✅ Firebase initialized');
    }
    
    // Step 1: Create or sign in user
    let userCredential;
    let isNewUser = false;
    
    try {
      console.log('🔐 Creating new user account...');
      userCredential = await createUserWithEmailAndPassword(auth, adminEmail, adminPassword);
      isNewUser = true;
      console.log('✅ New user account created:', userCredential.user.uid);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        console.log('👤 User already exists, signing in...');
        try {
          userCredential = await signInWithEmailAndPassword(auth, adminEmail, adminPassword);
          console.log('✅ Signed in with existing account:', userCredential.user.uid);
        } catch (signInError) {
          if (signInError.code === 'auth/wrong-password') {
            console.log('🔄 Wrong password, user exists but password is different');
            console.log('⚠️  Please use Firebase Console to reset password or use correct password');
            throw new Error('User exists but password is incorrect. Please reset password in Firebase Console.');
          } else {
            throw signInError;
          }
        }
      } else {
        throw error;
      }
    }
    
    const user = userCredential.user;
    console.log('👤 User UID:', user.uid);
    console.log('📧 User Email:', user.email);
    
    // Step 2: Create/update user document in Firestore with admin role
    console.log('📝 Creating admin user document in Firestore...');
    
    const userRef = doc(db, 'users', user.uid);
    const adminData = {
      email: adminEmail,
      role: 'admin',
      displayName: 'Tamil Selvan (Admin)',
      score: 0,
      challengesSolved: 0,
      solvedChallenges: [],
      progress: {
        beginner: { solved: 0, total: 20 },
        intermediate: { solved: 0, total: 25 },
        advanced: { solved: 0, total: 25 }
      },
      createdAt: serverTimestamp(),
      lastActivity: serverTimestamp(),
      rank: null,
      adminLevel: 'super',
      permissions: ['all'],
      isActive: true
    };
    
    await setDoc(userRef, adminData, { merge: true });
    console.log('✅ Admin user document created/updated in Firestore');
    
    // Step 3: Verify the document was created
    const userDoc = await getDoc(userRef);
    if (userDoc.exists()) {
      const userData = userDoc.data();
      console.log('✅ Verification: User document exists');
      console.log('👑 Role:', userData.role);
      console.log('📛 Display Name:', userData.displayName);
    } else {
      console.log('❌ Error: User document was not created');
    }
    
    // Step 4: Update local auth manager if available
    if (window.authManager) {
      console.log('🔄 Updating local auth manager...');
      window.authManager.currentUser = user;
      window.authManager.userRole = 'admin';
      window.authManager.updateUserInterface();
      console.log('✅ Auth manager updated');
    }
    
    // Step 5: Show admin panel
    console.log('🎛️  Activating admin panel...');
    
    // Show admin tab
    const adminTab = document.getElementById('admin-tab');
    if (adminTab) {
      adminTab.classList.remove('hidden');
      console.log('✅ Admin tab shown');
    }
    
    // Show admin section
    const adminSection = document.getElementById('admin-section');
    if (adminSection) {
      adminSection.classList.remove('hidden');
    }
    
    // Hide login screen, show main app
    const loginScreen = document.getElementById('login-screen');
    const mainApp = document.getElementById('main-app');
    
    if (loginScreen) loginScreen.classList.add('hidden');
    if (mainApp) mainApp.classList.remove('hidden');
    
    // Hide other sections
    ['challenges-section', 'leaderboard-section', 'profile-section'].forEach(id => {
      const section = document.getElementById(id);
      if (section) section.classList.add('hidden');
    });
    
    // Click admin tab to show admin panel
    if (adminTab) {
      adminTab.click();
    }
    
    // Load admin manager if available
    if (window.adminManager) {
      window.adminManager.loadOverview();
      console.log('✅ Admin manager loaded');
    }
    
    console.log('');
    console.log('🎉 === ADMIN ACCOUNT CREATION SUCCESSFUL ===');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: tamilselvanadmin63');
    console.log('👑 Role: admin');
    console.log('🆔 UID:', user.uid);
    console.log('✅ Admin panel activated');
    console.log('');
    
    // Show success alert
    alert('🎉 Admin account created successfully!\n\n📧 Email: <EMAIL>\n🔑 Password: tamilselvanadmin63\n👑 Role: admin\n\nAdmin panel is now active!');
    
    return {
      success: true,
      user: user,
      isNewUser: isNewUser,
      email: adminEmail,
      role: 'admin'
    };
    
  } catch (error) {
    console.error('❌ Error creating admin account:', error);
    alert('❌ Error creating admin account: ' + error.message);
    
    return {
      success: false,
      error: error.message
    };
  }
};

// Auto-run the admin creation
console.log('🚀 Auto-running admin account creation...');
createSpecificAdmin().then(result => {
  if (result.success) {
    console.log('🎉 Admin account creation completed successfully!');
  } else {
    console.log('❌ Admin account creation failed:', result.error);
  }
});

// Make function globally available
window.createAdmin = createSpecificAdmin;
