// Admin Authentication Module for The Wolf Challenge
import { auth, db } from './firebase-config.js';
import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged 
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js';
import { 
  doc, 
  setDoc, 
  getDoc, 
  serverTimestamp 
} from 'https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js';

class AdminAuth {
  constructor() {
    this.currentAdmin = null;
    this.isAuthenticated = false;
    this.authorizedAdminEmail = '<EMAIL>';
    this.authorizedAdminPassword = 'admintamilselvan';
    this.initializeAuth();
  }

  initializeAuth() {
    console.log('🐺 Initializing Admin Authentication...');
    
    // Listen for auth state changes
    onAuthStateChanged(auth, (user) => {
      if (user && this.isAuthorizedAdmin(user.email)) {
        this.handleAdminLogin(user);
      } else {
        this.handleAdminLogout();
      }
    });

    // Set up login form
    this.setupLoginForm();
  }

  setupLoginForm() {
    const loginForm = document.getElementById('admin-login-form');
    if (loginForm) {
      loginForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleLoginSubmit();
      });
    }
  }

  async handleLoginSubmit() {
    const emailInput = document.getElementById('admin-email');
    const passwordInput = document.getElementById('admin-password');
    
    const email = emailInput.value.trim();
    const password = passwordInput.value.trim();

    if (!email || !password) {
      this.showError('Please enter both email and password');
      return;
    }

    if (!this.isAuthorizedAdmin(email)) {
      this.showError('Unauthorized admin email');
      return;
    }

    try {
      console.log('🔐 Attempting admin login...');
      this.showLoading(true);

      // Try to sign in
      let userCredential;
      try {
        userCredential = await signInWithEmailAndPassword(auth, email, password);
        console.log('✅ Admin signed in successfully');
      } catch (signInError) {
        if (signInError.code === 'auth/user-not-found') {
          // Create admin account if it doesn't exist
          console.log('👤 Creating new admin account...');
          userCredential = await this.createAdminAccount(email, password);
        } else if (signInError.code === 'auth/wrong-password') {
          this.showError('Incorrect password');
          return;
        } else {
          throw signInError;
        }
      }

      // Verify admin role in Firestore
      await this.verifyAdminRole(userCredential.user);
      
      console.log('🎉 Admin authentication successful');
      
    } catch (error) {
      console.error('❌ Admin login failed:', error);
      this.showError('Login failed: ' + error.message);
    } finally {
      this.showLoading(false);
    }
  }

  async createAdminAccount(email, password) {
    try {
      console.log('🔧 Creating admin account...');
      
      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Create admin document in Firestore
      const adminData = {
        email: email,
        role: 'admin',
        displayName: 'Tamil Selvan (Admin)',
        createdAt: serverTimestamp(),
        lastActivity: serverTimestamp(),
        adminLevel: 'super',
        permissions: ['all'],
        isActive: true
      };

      await setDoc(doc(db, 'users', user.uid), adminData);
      console.log('✅ Admin account created successfully');

      return userCredential;
    } catch (error) {
      console.error('❌ Error creating admin account:', error);
      throw error;
    }
  }

  async verifyAdminRole(user) {
    try {
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      
      if (!userDoc.exists()) {
        throw new Error('Admin user document not found');
      }

      const userData = userDoc.data();
      if (userData.role !== 'admin') {
        throw new Error('User does not have admin role');
      }

      console.log('✅ Admin role verified');
      return userData;
    } catch (error) {
      console.error('❌ Admin role verification failed:', error);
      throw error;
    }
  }

  handleAdminLogin(user) {
    console.log('🎉 Admin logged in:', user.email);
    
    this.currentAdmin = user;
    this.isAuthenticated = true;

    // Store session
    localStorage.setItem('adminSession', JSON.stringify({
      uid: user.uid,
      email: user.email,
      timestamp: Date.now()
    }));

    // Show admin panel
    this.showAdminPanel();
  }

  handleAdminLogout() {
    console.log('🔓 Admin logged out');
    
    this.currentAdmin = null;
    this.isAuthenticated = false;

    // Clear session
    localStorage.removeItem('adminSession');

    // Show login screen
    this.showLoginScreen();
  }

  showAdminPanel() {
    document.getElementById('admin-login-screen').classList.add('hidden');
    document.getElementById('admin-panel').classList.remove('hidden');
    
    // Update admin email display (hidden for security)
    const emailDisplay = document.getElementById('admin-email-display');
    if (emailDisplay) {
      emailDisplay.textContent = '[HIDDEN FOR SECURITY]';
    }

    // Initialize admin panel
    if (window.adminPanel) {
      window.adminPanel.initialize();
    }
  }

  showLoginScreen() {
    document.getElementById('admin-panel').classList.add('hidden');
    document.getElementById('admin-login-screen').classList.remove('hidden');
  }

  showLoading(show) {
    const submitBtn = document.querySelector('#admin-login-form button[type="submit"]');
    if (submitBtn) {
      if (show) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>AUTHENTICATING...';
        submitBtn.disabled = true;
      } else {
        submitBtn.innerHTML = '<i class="fas fa-sign-in-alt mr-2"></i>ACCESS ADMIN PANEL';
        submitBtn.disabled = false;
      }
    }
  }

  showError(message) {
    // Remove existing error messages
    const existingError = document.querySelector('.admin-error');
    if (existingError) {
      existingError.remove();
    }

    // Create error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'admin-error neo-brutalist bg-red-500 text-white p-3 mt-4 text-sm';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle mr-2"></i>${message}`;

    // Add to form
    const form = document.getElementById('admin-login-form');
    if (form) {
      form.appendChild(errorDiv);
      
      // Auto-remove after 5 seconds
      setTimeout(() => {
        if (errorDiv.parentNode) {
          errorDiv.remove();
        }
      }, 5000);
    }
  }

  isAuthorizedAdmin(email) {
    return email && email.toLowerCase() === this.authorizedAdminEmail.toLowerCase();
  }

  async logout() {
    try {
      await signOut(auth);
      console.log('✅ Admin logged out successfully');
    } catch (error) {
      console.error('❌ Logout error:', error);
    }
  }

  // Check if admin is currently authenticated
  isAdminAuthenticated() {
    return this.isAuthenticated && this.currentAdmin;
  }

  // Get current admin user
  getCurrentAdmin() {
    return this.currentAdmin;
  }

  // Check session on page load
  checkSession() {
    const session = localStorage.getItem('adminSession');
    if (session) {
      try {
        const sessionData = JSON.parse(session);
        const now = Date.now();
        const sessionAge = now - sessionData.timestamp;
        
        // Session expires after 24 hours
        if (sessionAge < 24 * 60 * 60 * 1000) {
          console.log('🔄 Valid admin session found');
          return true;
        } else {
          console.log('⏰ Admin session expired');
          localStorage.removeItem('adminSession');
        }
      } catch (error) {
        console.error('❌ Invalid session data:', error);
        localStorage.removeItem('adminSession');
      }
    }
    return false;
  }
}

// Initialize admin authentication
const adminAuth = new AdminAuth();

// Make globally available
window.adminAuth = adminAuth;

// Export for module use
export default adminAuth;
