rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions for authentication and authorization
    function isAuth() { return request.auth != null; }
    function isAdmin() {
      return isAuth() &&
      exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    function isOwner(uid) { return isAuth() && request.auth.uid == uid; }

    // Users collection - full access for admins, limited for users
    match /users/{userId} {
      allow read: if isAuth();
      allow create: if isAuth();
      allow update: if isOwner(userId) || isAdmin();
      allow delete: if isAdmin();
    }

    // Challenges collection - read for authenticated, write for admins
    match /challenges/{challengeId} {
      allow read: if isAuth();
      allow write: if isAdmin();
    }

    // Submissions collection - create for users, full access for admins
    match /submissions/{submissionId} {
      allow read: if isAuth();
      allow create: if isAuth();
      allow update, delete: if isAdmin();
    }

    // Admin-only collections for system management
    match /score_events/{eventId} { allow read, write: if isAdmin(); }
    match /leaderboard/{document} { allow read: if isAuth(); allow write: if isAdmin(); }
    match /system_stats/{document} { allow read, write: if isAdmin(); }

    // Notifications and push subscriptions
    match /notifications/{notificationId} { allow read, write: if isAdmin(); allow read: if isAuth(); }
    match /push_subscriptions/{subscriptionId} { allow read, write: if isAdmin(); allow create: if isAuth(); }

    // Admin fallback - ensures all admin functions work
    match /{document=**} { allow read, write: if isAdmin(); }
  }
}
