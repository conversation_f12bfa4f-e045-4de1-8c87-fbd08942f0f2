rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Enhanced helper functions for authentication and authorization
    function isAuth() { return request.auth != null; }
    function isAdmin() {
      return isAuth() &&
      exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin' &&
      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.email == '<EMAIL>';
    }
    function isOwner(uid) { return isAuth() && request.auth.uid == uid; }
    function isResourceOwner() { return isAuth() && resource.data.userId == request.auth.uid; }
    function isAdminOrOwner(uid) { return isAdmin() || isOwner(uid); }

    // Users collection - comprehensive access control
    match /users/{userId} {
      allow read: if isAuth();
      allow create: if isAuth();
      allow update: if isAdminOrOwner(userId);
      allow delete: if isAdmin();
    }

    // Challenges collection - read for all authenticated, full control for admins
    match /challenges/{challengeId} {
      allow read: if isAuth();
      allow create, update, delete: if isAdmin();
    }

    // Submissions collection - flexible access for users and admins
    match /submissions/{submissionId} {
      allow read: if isAuth();
      allow create: if isAuth();
      allow update: if isResourceOwner() || isAdmin();
      allow delete: if isAdmin();
    }

    // Score events - admin-only for score management
    match /score_events/{eventId} {
      allow read, write: if isAdmin();
    }

    // Leaderboard - read for all, write for admins
    match /leaderboard/{document} {
      allow read: if isAuth();
      allow write: if isAdmin();
    }

    // System statistics - admin-only for analytics
    match /system_stats/{document} {
      allow read, write: if isAdmin();
    }

    // Admin logs - admin-only for audit trails
    match /admin_logs/{logId} {
      allow read, write: if isAdmin();
    }

    // Notifications - users read their own, admins manage all
    match /notifications/{notificationId} {
      allow read: if isAuth() && (isResourceOwner() || isAdmin());
      allow create, update, delete: if isAdmin();
    }

    // Push subscriptions - users manage their own, admins access all
    match /push_subscriptions/{subscriptionId} {
      allow read: if isAuth() && (isResourceOwner() || isAdmin());
      allow create: if isAuth();
      allow update, delete: if isAuth() && (isResourceOwner() || isAdmin());
    }

    // Competition settings - admin-only configuration
    match /settings/{settingId} {
      allow read: if isAuth();
      allow write: if isAdmin();
    }

    // Backup collections - admin-only for data management
    match /backups/{backupId} {
      allow read, write: if isAdmin();
    }

    // Reports - admin-only for analytics and reporting
    match /reports/{reportId} {
      allow read, write: if isAdmin();
    }

    // Admin fallback - ensures all admin functions work for any collection
    match /{document=**} {
      allow read, write: if isAdmin();
    }
  }
}
