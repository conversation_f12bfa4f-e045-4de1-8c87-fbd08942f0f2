rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isAuth() { return request.auth != null; }
    function isAdmin() { return isAuth() && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'; }
    function isOwner(uid) { return isAuth() && request.auth.uid == uid; }
    match /users/{userId} {
      allow read: if isAuth();
      allow create: if isOwner(userId) && resource.data.role == 'participant';
      allow update: if isOwner(userId) || isAdmin();
      allow delete: if isAdmin();
    }
    match /challenges/{challengeId} { allow read: if isAuth(); allow write: if isAdmin(); }
    match /submissions/{submissionId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isAuth() && isOwner(resource.data.userId);
      allow update, delete: if isAdmin();
    }
    match /score_events/{eventId} { allow read: if isOwner(resource.data.userId) || isAdmin(); allow write: if isAdmin(); }
    match /notifications/{notificationId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow update: if isOwner(resource.data.userId);
      allow create, delete: if isAdmin();
    }
    match /push_subscriptions/{subscriptionId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isAuth() && isOwner(resource.data.userId);
      allow update, delete: if isOwner(resource.data.userId) || isAdmin();
    } } }
