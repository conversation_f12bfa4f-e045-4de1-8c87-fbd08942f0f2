// Authentication Service Fix and Troubleshooting
// Run this script in the browser console to fix authentication issues

console.log('🐺 === AUTHENTICATION SERVICE FIX ===');

// Global fix functions
window.authFix = {
  
  // Test Firebase connection
  async testFirebaseConnection() {
    console.log('🐺 Testing Firebase connection...');
    
    try {
      // Check if Firebase is loaded
      if (typeof firebase === 'undefined' && typeof window.firebase === 'undefined') {
        console.log('📦 Loading Firebase modules...');
        
        // Import Firebase modules
        const { initializeApp } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-app.js');
        const { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js');
        const { getFirestore, doc, setDoc, serverTimestamp } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js');
        
        // Firebase config
        const firebaseConfig = {
          apiKey: "AIzaSyBWZWojxhjI3IWNo1km_XVcTXX5vYUV6Cc",
          authDomain: "wolf-ctf.firebaseapp.com",
          databaseURL: "https://wolf-ctf-default-rtdb.asia-southeast1.firebasedatabase.app",
          projectId: "wolf-ctf",
          storageBucket: "wolf-ctf.firebasestorage.app",
          messagingSenderId: "335565229802",
          appId: "1:335565229802:web:acb648a6bc2697ea1c9193",
          measurementId: "G-32N69ELWZN"
        };
        
        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        
        // Make globally available
        window.auth = auth;
        window.db = db;
        window.app = app;
        
        console.log('✅ Firebase modules loaded and initialized');
        return { auth, db, app };
      }
      
      // Use existing Firebase instances
      const auth = window.auth || window.firebase?.auth();
      const db = window.db || window.firebase?.firestore();
      
      console.log('✅ Firebase connection test passed');
      console.log('Auth instance:', auth);
      console.log('DB instance:', db);
      
      return { auth, db };
      
    } catch (error) {
      console.error('❌ Firebase connection test failed:', error);
      throw error;
    }
  },
  
  // Fix authentication service
  async fixAuthService() {
    console.log('🐺 Fixing authentication service...');
    
    try {
      // Test and initialize Firebase
      const { auth, db } = await this.testFirebaseConnection();
      
      // Test authentication
      console.log('🔐 Testing authentication...');
      
      // Check current auth state
      const currentUser = auth.currentUser;
      console.log('Current user:', currentUser?.email || 'None');
      
      // Set up auth state listener
      auth.onAuthStateChanged((user) => {
        if (user) {
          console.log('✅ Auth state changed: User signed in -', user.email);
          this.handleSuccessfulAuth(user);
        } else {
          console.log('🔓 Auth state changed: User signed out');
        }
      });
      
      console.log('✅ Authentication service fixed');
      return { auth, db };
      
    } catch (error) {
      console.error('❌ Failed to fix authentication service:', error);
      throw error;
    }
  },
  
  // Handle successful authentication
  handleSuccessfulAuth(user) {
    console.log('🎉 Authentication successful for:', user.email);
    
    // Check if user is admin
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    if (adminEmails.includes(user.email.toLowerCase())) {
      console.log('👑 Admin user detected, activating admin panel...');
      this.activateAdminPanel(user);
    }
  },
  
  // Activate admin panel
  activateAdminPanel(user) {
    try {
      // Set admin role in authManager if available
      if (window.authManager) {
        window.authManager.currentUser = user;
        window.authManager.userRole = 'admin';
        window.authManager.updateUserInterface();
      }
      
      // Show admin tab
      const adminTab = document.getElementById('admin-tab');
      if (adminTab) {
        adminTab.classList.remove('hidden');
        console.log('✅ Admin tab shown');
      }
      
      // Show admin section
      const adminSection = document.getElementById('admin-section');
      if (adminSection) {
        adminSection.classList.remove('hidden');
      }
      
      // Hide login screen, show main app
      const loginScreen = document.getElementById('login-screen');
      const mainApp = document.getElementById('main-app');
      
      if (loginScreen) loginScreen.classList.add('hidden');
      if (mainApp) mainApp.classList.remove('hidden');
      
      console.log('✅ Admin panel activated');
      alert('Admin panel activated! Click the ADMIN tab to access admin functions.');
      
    } catch (error) {
      console.error('❌ Error activating admin panel:', error);
    }
  },
  
  // Create admin account
  async createAdminAccount(email = '<EMAIL>', password = 'tamilselvanadmin63') {
    console.log('🐺 Creating admin account...');
    
    try {
      const { auth, db } = await this.fixAuthService();
      
      // Import auth functions
      const { signInWithEmailAndPassword, createUserWithEmailAndPassword } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-auth.js');
      const { doc, setDoc, serverTimestamp } = await import('https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore.js');
      
      let userCredential;
      
      try {
        // Try to sign in first
        userCredential = await signInWithEmailAndPassword(auth, email, password);
        console.log('✅ Signed in with existing account');
      } catch (signInError) {
        if (signInError.code === 'auth/user-not-found' || signInError.code === 'auth/wrong-password') {
          // Create new account
          userCredential = await createUserWithEmailAndPassword(auth, email, password);
          console.log('✅ Created new admin account');
        } else {
          throw signInError;
        }
      }
      
      // Set admin role in Firestore
      const userRef = doc(db, 'users', userCredential.user.uid);
      await setDoc(userRef, {
        email: email,
        role: 'admin',
        displayName: 'Tamil Selvan (Admin)',
        score: 0,
        challengesSolved: 0,
        progress: {
          beginner: { solved: 0, total: 10 },
          intermediate: { solved: 0, total: 20 },
          advanced: { solved: 0, total: 40 }
        },
        createdAt: serverTimestamp(),
        lastActivity: serverTimestamp()
      }, { merge: true });
      
      console.log('✅ Admin role set in Firestore');
      
      // Activate admin panel
      this.handleSuccessfulAuth(userCredential.user);
      
      return userCredential.user;
      
    } catch (error) {
      console.error('❌ Error creating admin account:', error);
      alert('Error creating admin account: ' + error.message);
      throw error;
    }
  },
  
  // Quick admin login
  async quickAdminLogin() {
    console.log('🐺 Quick admin login...');
    
    try {
      await this.createAdminAccount();
      console.log('✅ Quick admin login successful');
    } catch (error) {
      console.error('❌ Quick admin login failed:', error);
      
      // Fallback: Force admin panel
      console.log('🔄 Attempting fallback admin access...');
      this.forceAdminAccess();
    }
  },
  
  // Force admin access without authentication
  forceAdminAccess() {
    console.log('🐺 Forcing admin access...');
    
    try {
      // Show admin elements
      const adminTab = document.getElementById('admin-tab');
      const adminSection = document.getElementById('admin-section');
      const loginScreen = document.getElementById('login-screen');
      const mainApp = document.getElementById('main-app');
      
      if (adminTab) {
        adminTab.classList.remove('hidden');
        adminTab.click();
      }
      
      if (adminSection) {
        adminSection.classList.remove('hidden');
      }
      
      if (loginScreen) loginScreen.classList.add('hidden');
      if (mainApp) mainApp.classList.remove('hidden');
      
      // Hide other sections
      ['challenges-section', 'leaderboard-section', 'profile-section'].forEach(id => {
        const section = document.getElementById(id);
        if (section) section.classList.add('hidden');
      });
      
      // Load admin manager if available
      if (window.adminManager) {
        window.adminManager.loadOverview();
      }
      
      console.log('✅ Admin access forced');
      alert('Admin panel force-activated! You now have admin access.');
      
    } catch (error) {
      console.error('❌ Error forcing admin access:', error);
    }
  },
  
  // Run complete fix
  async runCompleteFix() {
    console.log('🐺 === RUNNING COMPLETE AUTHENTICATION FIX ===');
    
    try {
      // Step 1: Fix Firebase connection
      await this.fixAuthService();
      
      // Step 2: Try admin login
      await this.quickAdminLogin();
      
      console.log('✅ Complete authentication fix successful');
      
    } catch (error) {
      console.error('❌ Complete fix failed, trying fallback...');
      this.forceAdminAccess();
    }
  }
};

// Auto-run the fix
console.log('🚀 Auto-running authentication fix...');
authFix.runCompleteFix();
